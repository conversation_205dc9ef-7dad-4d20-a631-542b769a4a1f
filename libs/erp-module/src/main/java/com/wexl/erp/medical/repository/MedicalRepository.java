package com.wexl.erp.medical.repository;

import com.wexl.erp.medical.model.MedicalHistory;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MedicalRepository extends JpaRepository<MedicalHistory, Long> {
  Optional<MedicalHistory> findByStudentId(Long studentId);

  List<MedicalHistory> findAllByOrgSlug(String orgSlug);
}
