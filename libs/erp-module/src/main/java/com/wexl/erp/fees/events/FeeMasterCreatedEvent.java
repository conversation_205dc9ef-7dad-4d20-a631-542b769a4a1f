package com.wexl.erp.fees.events;

import com.wexl.erp.fees.dto.FeeDto;
import java.util.UUID;
import org.springframework.context.ApplicationEvent;

public class FeeMasterCreatedEvent extends ApplicationEvent {
  private final FeeDto.FeeMasterRequest request;
  private final String orgSlug;
  private final UUID feeMasterId;

  public FeeMasterCreatedEvent(UUID feeMasterId, FeeDto.FeeMasterRequest request, String orgSlug) {
    super(feeMasterId);

    this.feeMasterId = feeMasterId;
    this.request = request;
    this.orgSlug = orgSlug;
  }

  public FeeDto.FeeMasterRequest getRequest() {
    return request;
  }

  public String getOrgSlug() {
    return orgSlug;
  }

  @Override
  public UUID getSource() {
    return feeMasterId;
  }
}
