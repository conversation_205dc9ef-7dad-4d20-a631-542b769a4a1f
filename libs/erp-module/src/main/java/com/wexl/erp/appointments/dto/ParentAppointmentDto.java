package com.wexl.erp.appointments.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.erp.appointments.model.AppointmentStatus;
import java.util.List;
import lombok.Builder;

public class ParentAppointmentDto {

  public record Request(
      @JsonProperty("appointment_date") Long appointmentDate,
      @JsonProperty("appointment_reason") String appointmentReason,
      @JsonProperty("auth_id") List<String> authId,
      @JsonProperty("recipient_name") String recipientName,
      @JsonProperty("role") String role) {}

  @Builder
  public record Response(
      Long appointmentId,
      String guardianName,
      String studentName,
      String studentSection,
      String gradeName,
      String gradeSlug,
      Long studentId,
      Long guardianId,
      Long appointmentDate,
      String appointmentReason,
      @JsonProperty("recipient_name") String recipientName,
      @JsonProperty("role") String role,
      AppointmentStatus status,
      Long appliedDate,
      String accountHolder,
      String reviewedBy,
      Long reviewedOn) {}

  public record ApprovalRequest(@JsonProperty("status") AppointmentStatus status) {}
}
