package com.wexl.dps.reportcard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.InterReportCardData;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.InterOverAllReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.AssessmentEvaluationConfig;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.model.ReportCardConfig;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class InterFirstGradeReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final StudentAttributeService studentAttributeService;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private static final List<String> TWELFTH_GRADE_SLUGS = List.of("xii", "xiig");
  private final TermRepository termRepository;

  @Value("classpath:inter-term1-co-scholastic.json")
  private Resource resource;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request.termId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("inter-term1-report.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public InterOverAllReportDto.Body buildBody(User user, String orgSlug, Long termId) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var term = termRepository.findById(termId);
    var tableMarks = buildTableMarks(student, term.get().getSlug());

    String gradePerformanceText;
    if (TWELFTH_GRADE_SLUGS.contains(student.getSection().getGradeSlug())) {
      gradePerformanceText = "Record of Academic Performance-Class-XII";
    } else {
      gradePerformanceText = "Record of Academic Performance-Class-XI";
    }

    return InterOverAllReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .orgSlug(orgSlug)
        .termSlug(term.get().getSlug())
        .gradeSlug(student.getSection().getGradeSlug())
        .gradePerformanceText(gradePerformanceText)
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(buildFirstTable(tableMarks.firstTableMarks()))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .thirdTable(buildThirdTable(tableMarks.thirdTableMarks()))
        .fourthTable(buildFourthTable(tableMarks.fourthTableMarks()))
        .attendance(buildAttendanceForInterGrade(student.getId(), term.get().getSlug()))
        .build();
  }

  private InterOverAllReportDto.FourthTable buildFourthTable(
      List<InterOverAllReportDto.FirstTableMarks> fourthTableMarks) {
    return InterOverAllReportDto.FourthTable.builder()
        .title("VOCATIONAL SUBJECTS")
        .marks(fourthTableMarks)
        .build();
  }

  private InterOverAllReportDto.FirstTable buildFirstTable(
      List<InterOverAllReportDto.FirstTableMarks> firstTableMarks) {
    return InterOverAllReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .marks(firstTableMarks)
        .totals(buildTotals(firstTableMarks))
        .build();
  }

  private InterOverAllReportDto.ThirdTable buildThirdTable(
      InterOverAllReportDto.ThirdTableMarks thirdTableMarks) {
    return InterOverAllReportDto.ThirdTable.builder()
        .title("PART-II: CO-Scholastic Areas")
        .marks(thirdTableMarks)
        .build();
  }

  private InterOverAllReportDto.ThirdTableMarks buildThirdTableMarks(
      List<InterReportCardData> reportCardData) {

    var coScholasticAreaResources = getCoScholasticAreaResources();

    var reportDataMap =
        reportCardData.stream()
            .filter(s -> Objects.nonNull(s.getWexlSubject()))
            .collect(Collectors.groupingBy(s -> s.getWexlSubject().toLowerCase()));

    var thirdTableMarksBuilder = InterOverAllReportDto.ThirdTableMarks.builder();

    reportDataMap.forEach(
        (subject, reportCardDataList) -> {
          var coScholastic =
              coScholasticAreaResources.stream()
                  .filter(csa -> csa.subjectTitle().contains(subject))
                  .toList();

          List<InterOverAllReportDto.SubTable> subTables = new ArrayList<>();

          if (!coScholastic.isEmpty()) {
            reportCardDataList.forEach(
                rcd -> {
                  var coScholasticOptional =
                      coScholastic.stream()
                          .filter(csa -> csa.subjectName().contains(rcd.getSubjectName()))
                          .findAny();
                  if (coScholasticOptional.isEmpty()) {
                    return;
                  }
                  String grade;
                  if (rcd.getMarks() != null && rcd.getMarks() != 0) {
                    grade =
                        pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(rcd.getMarks()));
                  } else {
                    grade =
                        Objects.nonNull(rcd.getRemarks()) ? rcd.getRemarks().substring(0, 2) : "-";
                  }
                  subTables.add(
                      InterOverAllReportDto.SubTable.builder()
                          .subjectName(rcd.getSubjectName())
                          .term1Grade(grade)
                          .descriptive(coScholasticOptional.get().descriptive())
                          .build());
                });
          }
          if (subject.toLowerCase().contains("life-skills")) {
            thirdTableMarksBuilder.subTable1(subTables);
          } else if (subject.toLowerCase().contains("health-and-physical-education")) {
            thirdTableMarksBuilder.subTable2(subTables);
          } else if (subject.toLowerCase().contains("work-education")) {
            thirdTableMarksBuilder.subTable3(subTables);
          }
        });
    return thirdTableMarksBuilder.build();
  }

  private List<InterOverAllReportDto.InterTerm1CoScholastic> getCoScholasticAreaResources() {

    List<InterOverAllReportDto.InterTerm1CoScholastic> coScholasticAreas = new ArrayList<>();
    try {
      var objectMapper = new ObjectMapper();
      coScholasticAreas =
          objectMapper.readValue(ResourceUtils.asString(resource), new TypeReference<>() {});
      if (Objects.isNull(coScholasticAreas) || coScholasticAreas.isEmpty()) {
        return coScholasticAreas;
      }
    } catch (Exception ex) {
      log.error(
          "Unable to process the resource [inter-term1-co-scholastic.json] from the classpath", ex);
      return coScholasticAreas;
    }
    return coScholasticAreas;
  }

  private InterOverAllReportDto.SecondTable buildSecondTable(
      List<InterOverAllReportDto.SecondTableMarks> secondTables) {
    return InterOverAllReportDto.SecondTable.builder()
        .title("INTERNAL SUBJECTS (3 Point Scale)")
        .marks(secondTables)
        .build();
  }

  private InterOverAllReportDto.Totals buildTotals(
      List<InterOverAllReportDto.FirstTableMarks> firstTableMarks) {
    double totalMarksScored =
        firstTableMarks.stream()
            .map(InterOverAllReportDto.FirstTableMarks::total)
            .filter(Objects::nonNull)
            .mapToDouble(Double::parseDouble)
            .sum();

    double totalExamMarks =
        firstTableMarks.stream()
            .map(InterOverAllReportDto.FirstTableMarks::totalExamMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Long::doubleValue)
            .sum();

    double overallPercentage =
        (totalExamMarks == 0)
            ? 0
            : Double.parseDouble(
                String.format("%.2f", ((totalMarksScored / totalExamMarks) * 100)));

    String grade =
        (totalExamMarks == 0)
            ? "N/A"
            : pointScaleEvaluator.evaluate("4point", BigDecimal.valueOf(overallPercentage));

    return InterOverAllReportDto.Totals.builder()
        .annualExam(0L)
        .total((long) totalMarksScored)
        .grade(grade)
        .overallPercentage(overallPercentage)
        .build();
  }

  private InterOverAllReportDto.TableMarks buildTableMarks(Student student, String termSlug) {
    var section = student.getSection();
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of(termSlug), student.getSection().getGradeSlug());

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());

    var filteredReportCardConfigs =
        reportCardConfigs.stream()
            .filter(
                rc ->
                    rc.getReportCardConfigDetails().stream()
                        .anyMatch(
                            rccd -> rccd.getTermAssessment().getTerm().getSlug().equals(termSlug)))
            .toList();

    var assessmentCategories =
        filteredReportCardConfigs.stream()
            .max(Comparator.comparing(ReportCardConfig::getId))
            .map(ReportCardConfig::getReportCardConfigDetails)
            .orElse(Collections.emptyList())
            .stream()
            .map(ReportCardConfigDetail::getAssessmentEvaluationConfig)
            .filter(Objects::nonNull)
            .map(AssessmentEvaluationConfig::getAssessmentCategories)
            .flatMap(Set::stream)
            .collect(Collectors.toSet());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByAssessments(
            student.getId(), termAssessmentIds);

    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var highestMarksReportData =
        reportCardConfigDataRepository.getHighestMarksReportData(
            student.getSection().getGradeSlug(),
            student.getSection().getOrganization(),
            assessmentCategories.isEmpty() ? null : assessmentCategories);
    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();
    var scholasticOptionalDataList =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();

    return InterOverAllReportDto.TableMarks.builder()
        .firstTableMarks(sortTable(buildTableMarks(scholasticDataList, highestMarksReportData)))
        .secondTableMarks(buildSecondTableMarks(coScholasticData))
        .thirdTableMarks(buildThirdTableMarks(coScholasticOptionalData))
        .fourthTableMarks(
            sortTable(buildTableMarks(scholasticOptionalDataList, highestMarksReportData)))
        .build();
  }

  private List<InterOverAllReportDto.FourthTableMarks> buildFourthTableMarks(
      List<InterReportCardData> interReportCardData) {
    List<InterOverAllReportDto.FourthTableMarks> marks = new ArrayList<>();

    var reportCardDataMap =
        interReportCardData.stream()
            .collect(Collectors.groupingBy(InterReportCardData::getSubjectName));

    reportCardDataMap.forEach(
        (subject, reportCardData) -> {
          var markSecured =
              reportCardData.stream().mapToDouble(InterReportCardData::getMarks).sum();

          var grade = pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(markSecured));
          marks.add(
              InterOverAllReportDto.FourthTableMarks.builder()
                  .subjectName(subject)
                  .term1Grade(grade)
                  .build());
        });
    return marks;
  }

  private List<InterOverAllReportDto.SecondTableMarks> buildSecondTableMarks(
      List<InterReportCardData> interReportCardData) {
    List<InterOverAllReportDto.SecondTableMarks> marks = new ArrayList<>();

    var reportCardDataMap =
        interReportCardData.stream()
            .collect(Collectors.groupingBy(InterReportCardData::getSubjectName));

    reportCardDataMap.forEach(
        (subject, reportCardData) -> {
          var markSecured =
              reportCardData.stream().mapToDouble(InterReportCardData::getMarks).sum();

          var grade =
              reportCardData.stream()
                  .map(
                      term -> {
                        if (Objects.isNull(term.getIsAttended())
                            || Objects.isNull(term.getMarks())) {
                          return null;
                        } else if ("false".equalsIgnoreCase(term.getIsAttended().toString())) {
                          return term.getRemarks() == null
                              ? "AB"
                              : term.getRemarks().substring(0, 2).toUpperCase();
                        }
                        return pointScaleEvaluator.evaluate(
                            "3point", BigDecimal.valueOf(markSecured));
                      })
                  .findFirst()
                  .orElse("");

          marks.add(
              InterOverAllReportDto.SecondTableMarks.builder()
                  .subjectName(subject)
                  .term1Grade(grade)
                  .build());
        });
    return marks;
  }

  private String calculateGrade(Long marks, Long totalMarks) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate("4point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  public List<InterOverAllReportDto.FirstTableMarks> buildTableMarks(
      List<InterReportCardData> scholasticDataList,
      List<InterReportCardData> highestMarksReportData) {
    List<InterOverAllReportDto.FirstTableMarks> marksList = new ArrayList<>();
    var subjects =
        scholasticDataList.stream().map(InterReportCardData::getSubjectName).distinct().toList();

    var highestMarksReportMap =
        highestMarksReportData.stream()
            .sorted(Comparator.comparingLong(InterReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    InterReportCardData::getSubjectName, LinkedHashMap::new, Collectors.toList()));

    for (String subject : subjects) {
      var subjectData =
          scholasticDataList.stream()
              .filter(data -> data.getSubjectName().equals(subject))
              .toList();
      var theoryMarks = getMarks("theory", subjectData);
      var practicalMarks = getMarks("practical", subjectData);
      var totalMarks =
          (theoryMarks == null ? 0d : theoryMarks) + (practicalMarks == null ? 0d : practicalMarks);
      var totalExamMarks = subjectData.stream().mapToLong(InterReportCardData::getTotalMarks).sum();
      var highestMarks = getHighestMarks(highestMarksReportMap.get(subject));
      marksList.add(
          InterOverAllReportDto.FirstTableMarks.builder()
              .subject(subject)
              .seqNo(subjectData.get(0).getSeqNo())
              .theoryMarks(calculateMarks("theory", subjectData))
              .practicalMarks(calculateMarks("practical", subjectData))
              .highestMarks(highestMarks)
              .totalMarksScored((long) totalMarks)
              .total(String.format("%.2f", totalMarks))
              .totalExamMarks(totalExamMarks)
              .build());
    }
    return marksList;
  }

  private String calculateMarks(String assessmentSlug, List<InterReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> data.getAssessmentSlug().equalsIgnoreCase(assessmentSlug))
        .findFirst()
        .map(
            data -> {
              if (Objects.isNull(data.getIsAttended()) || Objects.isNull(data.getMarks())) {
                return null;
              } else if (Boolean.FALSE.equals(data.getIsAttended())) {
                return data.getRemarks() != null && !data.getRemarks().isEmpty()
                    ? data.getRemarks().substring(0, 2).toUpperCase()
                    : "AB";
              }
              return String.format("%.2f", data.getMarks());
            })
        .orElse(null);
  }

  private Double getMarks(String assessmentSlug, List<InterReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> data.getAssessmentSlug().equalsIgnoreCase(assessmentSlug))
        .map(InterReportCardData::getMarks)
        .filter(Objects::nonNull)
        .mapToDouble(Double::doubleValue)
        .average()
        .orElse(0.0);
  }

  private String getHighestMarks(List<InterReportCardData> subjectData) {
    if (Objects.isNull(subjectData)) {
      return null;
    }
    double sumOfHighestMarks =
        subjectData.stream()
            .filter(s -> Objects.nonNull(s.getHighestMarks()))
            .mapToDouble(InterReportCardData::getHighestMarks)
            .sum();
    return String.format("%.2f", sumOfHighestMarks);
  }

  private List<InterOverAllReportDto.FirstTableMarks> sortTable(
      List<InterOverAllReportDto.FirstTableMarks> firstTableMarks) {
    List<InterOverAllReportDto.FirstTableMarks> firstTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(InterOverAllReportDto.FirstTableMarks::seqNo))
            .toList();
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      InterOverAllReportDto.FirstTableMarks mark = sortedFirstTable.get(i);
      firstTable.add(
          InterOverAllReportDto.FirstTableMarks.builder()
              .sNo(i + 1L)
              .subject(mark.subject())
              .theoryMarks(mark.theoryMarks())
              .practicalMarks(mark.practicalMarks())
              .highestMarks(mark.highestMarks())
              .totalExamMarks(mark.totalExamMarks())
              .total(mark.total())
              .build());
    }
    return firstTable;
  }

  private InterOverAllReportDto.Attendance buildAttendanceForInterGrade(
      long studentId, String termSlug) {
    var termAssessment = termAssessmentRepository.getBySlug("theory");
    if (termAssessment.isEmpty()) {
      return InterOverAllReportDto.Attendance.builder().build();
    }
    var filteredTermAssessment =
        termAssessment.stream().filter(ta -> termSlug.equals(ta.getTerm().getSlug())).toList();
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, filteredTermAssessment.get(0).getId());
    if (studentAttendance.isEmpty()) {
      return InterOverAllReportDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return InterOverAllReportDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return InterOverAllReportDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }

  public List<ReportCardConfigDto.GradeAndPercentage> getGradeAndPercentage(
      List<Student> students, String gradeSlug) {
    List<ReportCardConfigDto.GradeAndPercentage> responseList = new ArrayList<>();

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(List.of("t2"), gradeSlug);

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
    var studentIds = students.stream().map(Student::getId).toList();
    var classWiseTopperMarks =
        reportCardConfigDataRepository.getClassWiseTopperMarks(studentIds, termAssessmentIds);

    var reportCardDataMap =
        classWiseTopperMarks.stream()
            .collect(Collectors.toMap(InterReportCardData::getStudentId, Function.identity()));

    students.forEach(
        student -> {
          try {
            var data = reportCardDataMap.get(student.getId());
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .percentage(
                        Objects.nonNull(data) ? data.getOverallPercentage().toString() : "-")
                    .student(student)
                    .build());
          } catch (Exception ex) {
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .grade("NA")
                    .percentage("-")
                    .student(student)
                    .build());
          }
        });
    return responseList;
  }
}
