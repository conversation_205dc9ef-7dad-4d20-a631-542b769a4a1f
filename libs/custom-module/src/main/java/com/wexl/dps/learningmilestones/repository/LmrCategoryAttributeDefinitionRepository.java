package com.wexl.dps.learningmilestones.repository;

import com.wexl.dps.learningmilestones.model.LmrCategoryAttributeDefinition;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface LmrCategoryAttributeDefinitionRepository
    extends JpaRepository<LmrCategoryAttributeDefinition, Long> {
  List<LmrCategoryAttributeDefinition> findAllByLmrCategoryAttributeId(Long lmrCategoryAttributeId);
}
