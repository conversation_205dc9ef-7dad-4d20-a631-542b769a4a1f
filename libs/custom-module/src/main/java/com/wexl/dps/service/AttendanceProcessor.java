package com.wexl.dps.service;

import com.wexl.dps.dto.DpsAttendanceSyncDto;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.OfflineTestScheduleDto;
import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.repository.StudentRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class AttendanceProcessor {
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final StudentRepository studentRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  public static final String HALF_YEARLY_EXAMINATION = "ae";

  public void erpSyncAttendance(DpsAttendanceSyncDto.AttendanceAndRemarksRequest response) {
    var students =
        studentRepository.findStudentByUserExternalRef(
            response.attendance().stream()
                .map(DpsAttendanceSyncDto.Attendance::studentCode)
                .toList());
    var sections =
        students.stream()
            .map(Student::getSection)
            .distinct()
            .map(section -> section.getUuid().toString())
            .toList();
    var offlineTestDefinitions =
        offlineTestDefinitionRepository.findBySectionUuidInAndAssessmentSlug(
            sections, HALF_YEARLY_EXAMINATION);
    var studentCodeMapper =
        response.attendance().stream()
            .collect(Collectors.groupingBy(DpsAttendanceSyncDto.Attendance::studentCode));
    List<OfflineTestScheduleDto.Attendance> attendanceList = new ArrayList<>();
    for (OfflineTestDefinition otd : offlineTestDefinitions) {
      var sectionStudents =
          students.stream()
              .filter(
                  student -> student.getSection().getUuid().toString().equals(otd.getSectionUuid()))
              .toList();
      for (Student student : sectionStudents) {
        var std = studentCodeMapper.get(student.getUserInfo().getExternalRef()).get(0);
        attendanceList.add(
            OfflineTestScheduleDto.Attendance.builder()
                .presentDays(std.presentDays() != null ? std.presentDays() : 0)
                .studentId(student.getId())
                .build());
      }
      var request =
          OfflineTestScheduleDto.AttendanceAndRemarksRequest.builder()
              .totalAttendance(response.totalAttendance())
              .attendance(attendanceList)
              .build();
      offlineTestScheduleService.saveAttendance(otd.getId(), request);
    }
  }
}
