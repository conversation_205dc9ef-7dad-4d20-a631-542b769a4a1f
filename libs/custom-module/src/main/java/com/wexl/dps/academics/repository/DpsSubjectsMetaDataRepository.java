package com.wexl.dps.academics.repository;

import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DpsSubjectsMetaDataRepository extends SubjectsMetaDataRepository {

  @Query(
      value =
          """
                    SELECT s.* FROM subject_metadata s WHERE s.org_slug = :orgSlug AND s.category = :category AND s.type = :subjectsType and s.board_slug = :boardSlug
                    """,
      nativeQuery = true)
  List<SubjectsMetaData> findByOrgSlugAndCategoryEnumAndSubjectsTypeEnum(
      String orgSlug, String category, String subjectsType, String boardSlug);
}
