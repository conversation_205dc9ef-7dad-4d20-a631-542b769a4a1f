package com.wexl.reportcardhistory.Repository;

import com.wexl.retail.metrics.reportcards.dto.StudentHistoricalReportType;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentHistoricalReportTypeRepository
    extends JpaRepository<StudentHistoricalReportType, Long> {

  Optional<StudentHistoricalReportType> findByOrgSlugAndTestDefinitionId(
      String orgSlug, Long testDefinitionId);
}
