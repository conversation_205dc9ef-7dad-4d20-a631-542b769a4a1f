package com.wexl.saisenior.reportcard.dto;

import java.util.List;
import lombok.Builder;

public record SaiSenior9to12Term2ReportDto() {

  @Builder
  public record Response(SaiSeniorUpperGradeDto.Header header, SaiSeniorUpperGradeDto.Body body) {}

  @Builder
  public record Body(
      String name,
      String className,
      String rollNumber,
      String admissionNumber,
      String dateOfBirth,
      String fathersName,
      String mothersName,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      String generalRemark) {}

  @Builder
  public record FirstTable(List<Marks> subject) {}

  @Builder
  public record Marks(
      Long colSpan,
      String name,
      Integer seqNo,
      Double faTotalMarks,
      Double fa2TotalMarks,
      Double saTotalMarks,
      Double sa2TotalMarks,
      String term1IA,
      String term2IA,
      String term1SA,
      String term2SA,
      String percentage) {}

  @Builder
  public record SecondTable(List<SecondTableMarks> marks) {}

  @Builder
  public record SecondTableMarks(String name, String grade) {}

  @Builder
  public record ThirdTable(
      String result, Double percentage, Double attendance, String rank, Double date) {}
}
