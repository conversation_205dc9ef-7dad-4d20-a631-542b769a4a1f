package com.wexl.saisenior.reportcard.dto;

import java.util.List;
import lombok.Builder;

public record SaiSenior9to12ReportDto() {

  @Builder
  public record Response(SaiSeniorUpperGradeDto.Header header, SaiSeniorUpperGradeDto.Body body) {}

  @Builder
  public record Body(
      String name,
      String className,
      String rollNumber,
      String admissionNo,
      String dateOfBirth,
      String fathersName,
      String mothersName,
      FirstTable firstTable,
      SecondTable secondTable,
      String generalRemark) {}

  @Builder
  public record FirstTable(List<Marks> subject) {}

  @Builder
  public record Marks(
      Long colSpan,
      String name,
      Integer seqNo,
      Double faTotalMarks,
      Double saTotalMarks,
      String internalAssessment,
      String summativeAssessment,
      Double percentage) {}

  @Builder
  public record SecondTable(
      String result, Double percentage, Double attendance, String rank, Double date) {}
}
