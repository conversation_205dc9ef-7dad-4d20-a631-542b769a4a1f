package com.wexl.pallavi.preprimary.controller;

import com.wexl.pallavi.preprimary.dto.ProgressCardDto;
import com.wexl.pallavi.preprimary.service.HolisticReportDataMigrationService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class HolisticDataMigrationController {

  private final HolisticReportDataMigrationService holisticReportDataMigrationService;

  @PostMapping("/orgs/{orgSlug}/holistic_data_migration")
  public ResponseEntity<Void> holisticDataMigration(
      @PathVariable("orgSlug") String orgSlug,
      @RequestBody ProgressCardDto.HolisticReportRequest request) {
    holisticReportDataMigrationService.migrateHolisticData(orgSlug, request);
    return ResponseEntity.ok().build();
  }
}
