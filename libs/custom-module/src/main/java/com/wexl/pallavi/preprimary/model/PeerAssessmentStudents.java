package com.wexl.pallavi.preprimary.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pallavi_pre_primary_peerAssessment_students")
public class PeerAssessmentStudents extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  private PeerAssessmentTypes term1;
  private PeerAssessmentTypes term2;

  @ManyToOne
  @JoinColumn(name = "pallavi_pre_primary_peerAssessment_id")
  private PeerAssessments peerAssessments;
}
