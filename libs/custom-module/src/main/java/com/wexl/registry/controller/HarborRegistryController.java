package com.wexl.registry.controller;

import com.wexl.registry.dto.HarborDto;
import com.wexl.registry.service.HarborRegistryService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/public/harbor-registry")
@RequiredArgsConstructor
public class HarborRegistryController {
  private static final Logger log = LoggerFactory.getLogger(HarborRegistryController.class);

  private final HarborRegistryService harborRegistryService;

  /**
   * Cleans up old images from all repositories in the wexledu-nonprod project. Keeps only images
   * from the last 14 days and limits to 10 images per repository.
   */
  @DeleteMapping("/cleanup-images")
  public void cleanupOldImages() {
    log.info("Received request to cleanup old images");
    harborRegistryService.cleanupOldImages();
    log.info("Completed cleanup of old images");
  }

  @PostMapping("/retag-images")
  public void retagImages(@Valid @RequestBody HarborDto.RetagRequest retagRequest) {
    // retagRequest contains the image and version.
    // example - image - texmath, version - 1.0.61386
    // The image dcr.wexledu.com/wexledu-nonprod/texmath:1.0.61386 will need to be retagged to
    // dcr.wexledu.com/wexledu-prod/texmath:1.0.61386
    log.info(
        "Received request to retag image: {} with version: {}",
        retagRequest.image(),
        retagRequest.version());
    harborRegistryService.retagImage(retagRequest.image(), retagRequest.version());
    log.info(
        "Completed retagging of image: {} with version: {}",
        retagRequest.image(),
        retagRequest.version());
  }
}
