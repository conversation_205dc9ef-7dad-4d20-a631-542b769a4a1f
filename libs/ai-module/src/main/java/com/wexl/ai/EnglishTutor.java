package com.wexl.ai;

import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis.AiQuestionAnalysisResponseList;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;
import dev.langchain4j.service.spring.AiService;

@AiService
public interface EnglishTutor {

  @SystemMessage(
      "You are an English tutor. You need to perform evaluation of the answers written by the students based on questions provided.")
  @UserMessage("{{userMessage}}")
  AiQuestionAnalysisResponseList performEnglishAnalysis(String userMessage);

  @SystemMessage(
      "you are a School teacher. You need to perform analysis of the answer written by the students based on answer provided")
  @UserMessage("{{userMessage}}")
  ExamAnalysis.AnswerReAnalysis answerReAnalysis(String userMessage);

  @UserMessage("{{userMessage}}")
  ExamAnalysis.TestEnrichmentAiResponse enrichQuestion(@V("userMessage") String userMessage);
}
