package com.wexl.retail.pdf.viewer.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommentRequest {
  private long id;
  private long oldId;
  private String username;
  private String comment;
  private long parentId;
  private long annotationId;
  private String modified;
  private String oldModified;
  private List<CommentsReviewHistoryRequest> reviewStatuses;

  @JsonProperty("dateCreated")
  private Timestamp createdAt;

  @JsonProperty("dateModified")
  private Timestamp updatedAt;
}
