package com.wexl.retail.thread.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record ThreadDto() {

  public record CreateThreadRequest(
      String title,
      String question,
      List<String> tags,
      @JsonProperty("assigned_to") String assignedTo) {}

  @Builder
  public record ThreadResponse(
      Long Id,
      String title,
      String question,
      List<String> tags,
      @JsonProperty("reply_count") Long replyCount,
      Long createdAt,
      @JsonProperty("created_by") String studentName,
      @JsonProperty("assigned_to") String teacherName) {}

  @Builder
  public record ThreadByIdResponse(
      @JsonProperty("thread") ThreadResponse threadResponse,
      @JsonProperty("replies") List<ThreadReply> threadReplies) {}

  @Builder
  public record ThreadReply(
      Long id, String reply, String replied_by, List<ThreadReplyComments> replyComments) {}

  @Builder
  public record ThreadReplyComments(
      Long id, String reply, String replied_by, Boolean isTeacher, Boolean isStudent) {}

  public record CreateThreadReplyRequest(
      String reply, @JsonProperty("thread_reply_comment_id") Long threadReplyCommentId) {}
}
