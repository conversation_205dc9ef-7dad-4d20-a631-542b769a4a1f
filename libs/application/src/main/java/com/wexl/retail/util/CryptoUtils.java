package com.wexl.retail.util;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
public class CryptoUtils {

  private static final String ALGORITHM = "AES";
  private static final int KEY_SIZE_IN_BITS = 128;

  @Value("${app.security.secret}")
  private String secret;

  private SecretKey generateStandardSecretKey() throws NoSuchAlgorithmException {
    KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
    keyGenerator.init(KEY_SIZE_IN_BITS);
    return new SecretKeySpec(secret.getBytes(), ALGORITHM);
  }

  private SecretKey generateSecretKey(String newSecret) throws NoSuchAlgorithmException {
    KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
    keyGenerator.init(KEY_SIZE_IN_BITS);
    return new SecretKeySpec(newSecret.getBytes(), ALGORITHM);
  }

  public IvParameterSpec generateStandardIV() {
    byte[] iv = new byte[16];
    new SecureRandom().nextBytes(iv);
    return new IvParameterSpec(iv);
  }

  public IvParameterSpec generateIVFromEncodedString(String iv) {
    return new IvParameterSpec(Base64.getDecoder().decode(iv));
  }

  public String encrypt(String payload) {
    try {
      Cipher cipher = Cipher.getInstance("%s/CBC/PKCS5Padding".formatted(ALGORITHM));

      final var ivSpec = generateStandardIV();
      cipher.init(Cipher.ENCRYPT_MODE, generateStandardSecretKey(), ivSpec);
      byte[] cipherText = cipher.doFinal(payload.getBytes());
      String encodedCipher = Base64.getEncoder().encodeToString(cipherText);
      String ivString = Base64.getEncoder().encodeToString(ivSpec.getIV());
      return "%s:%s:%s".formatted(ivString, secret, encodedCipher);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return "";
  }

  public String decrypt(String cipherText) {
    if (StringUtils.isBlank(cipherText)) {
      return "";
    }
    String[] split = cipherText.split(":");
    if (split.length != 3) {
      return "";
    }
    try {
      String iv = split[0];
      String secretKey = split[1];
      String encodedCipher = split[2];
      Cipher cipher = Cipher.getInstance("%s/CBC/PKCS5Padding".formatted(ALGORITHM));
      cipher.init(
          Cipher.DECRYPT_MODE, generateSecretKey(secretKey), generateIVFromEncodedString(iv));
      byte[] plainText = cipher.doFinal(Base64.getDecoder().decode(encodedCipher));
      return new String(plainText);
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
    }
    return "";
  }
}
