package com.wexl.retail.test.competitive.validators;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class IITTestDefinitionValidator implements TestDefinitionValidator {

  private final String[] sectionNames =
      new String[] {
        "Physics Section A",
        "Physics Section B",
        "Chemistry Section A",
        "Chemistry Section B",
        "Mathematics Section A",
        "Mathematics Section B"
      };
  private final Integer[] sectionQuestionCount = new Integer[] {20, 5, 20, 5, 20, 5};

  private final String[] questionTypes = new String[] {"MCQ", "NAT"};

  @Override
  public void validate(TestDefinition testDefinition) {
    if (testDefinition.getTestDefinitionSections().size() != 6) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Sections.IIT");
    }
    List<TestDefinitionSection> sortedSections =
        testDefinition.getTestDefinitionSections().stream()
            .sorted(Comparator.comparingLong(TestDefinitionSection::getSequenceNumber))
            .toList();
    for (int i = 0; i < sortedSections.size(); i++) {
      if (!sortedSections.get(i).getName().equals(sectionNames[i])) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Invalid.SectionNames.IIT");
      }
    }
    for (int i = 0; i < sortedSections.size(); i++) {
      if (sortedSections.get(i).getTestQuestions().size() != sectionQuestionCount[i]) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Invalid.SectionQuestionCount.IIT");
      }
    }

    for (int i = 0; i < sortedSections.size(); i++) {
      if (i == 0 || i == 2 || i == 4) {
        validateQuestionTypeInSections(sortedSections, i, QuestionType.MCQ);
      } else {
        validateQuestionTypeInSections(sortedSections, i, QuestionType.NAT);
      }
    }
  }

  private void validateQuestionTypeInSections(
      List<TestDefinitionSection> sortedSections, int i, QuestionType questionType) {
    final TestDefinitionSection testDefinitionSection = sortedSections.get(i);
    testDefinitionSection
        .getTestQuestions()
        .forEach(
            testQuestion -> {
              if (!Arrays.asList(questionTypes).contains(testQuestion.getType())) {
                throw new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Invalid.QuestionType.IIT",
                    new String[] {testDefinitionSection.getName(), questionType.name()});
              }

              if (testQuestion.getMarks() != 4 || testQuestion.getNegativeMarks() != 1) {
                throw new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Invalid.QuestionMarks",
                    new String[] {"IIT", testDefinitionSection.getName()});
              }
            });
  }

  @Override
  public boolean supports(TestCategory name) {
    return TestCategory.IIT.equals(name);
  }

  @Override
  public List<TestScheduleStudentAnswer> processOptionalQuestions(
      List<TestScheduleStudentAnswer> tssa, List<TestDefinitionSection> sections) {
    return tssa;
  }
}
