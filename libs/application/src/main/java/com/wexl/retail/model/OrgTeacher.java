package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

public interface OrgTeacher {

  String getEmail();

  String getFirstName();

  String getLastName();

  String getMobileNumber();

  String getAuthUserId();

  long getTeacherId();

  Date getLastLoginTime();

  String getAdmin();

  @JsonIgnore
  String getUsername();

  @JsonIgnore
  Timestamp getDeletedAt();

  List<String> getSubjects();

  String getOrgName();
}
