package com.wexl.retail.communications.logbook.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.communications.logbook.dto.LogBookDto;
import com.wexl.retail.communications.logbook.service.LogBookService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherAuthId}/logbooks")
@RequiredArgsConstructor
public class LogBookController {

  private final LogBookService logBookService;

  @IsOrgAdminOrTeacher
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void saveLogBook(
      @RequestBody LogBookDto.Request request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId) {
    logBookService.saveLogBook(orgSlug, request, teacherAuthId);
  }

  @GetMapping
  public List<LogBookDto.Response> getLogBook(
      @RequestParam(value = "board_slug", required = false) List<String> boardSlug,
      @RequestParam(value = "grade_slug", required = false) List<String> gradeSlug,
      @RequestParam(value = "section_uuid", required = false) List<String> sectionUuid,
      @RequestParam(value = "student_id", required = false) List<Long> studentId,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId) {
    return logBookService.getLogBook(
        orgSlug, teacherAuthId, boardSlug, gradeSlug, sectionUuid, studentId);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/{id}")
  public void updateLogBook(
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId,
      @PathVariable("id") Long logBookId,
      @RequestBody LogBookDto.Request request) {
    logBookService.updateLogBook(orgSlug, logBookId, request, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/{id}")
  public void deleteLogBook(@PathVariable("id") Long logBookId) {
    logBookService.deleteLogBook(logBookId);
  }
}
