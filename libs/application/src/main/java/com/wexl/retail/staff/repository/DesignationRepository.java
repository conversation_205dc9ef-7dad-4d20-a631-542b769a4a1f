package com.wexl.retail.staff.repository;

import com.wexl.retail.staff.model.Designation;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DesignationRepository extends JpaRepository<Designation, Long> {
  Optional<Designation> findByName(String name);

  List<Designation> findByDepartmentId(Long departmentId);
}
