package com.wexl.retail.staff.controller;

import com.wexl.retail.staff.dto.StaffDto;
import com.wexl.retail.staff.service.StaffService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/staff")
@RequiredArgsConstructor
@Slf4j
public class StaffController {

  private final StaffService staffService;

  @PostMapping
  public ResponseEntity<StaffDto.StaffResponse> createStaff(
      @RequestBody StaffDto.StaffRequest request, @PathVariable String orgSlug) {
    log.info("Creating new staff member: {}", request.firstName());
    return new ResponseEntity<>(staffService.createStaff(request, orgSlug), HttpStatus.CREATED);
  }

  @GetMapping("/{id}")
  public ResponseEntity<StaffDto.StaffResponse> getStaffById(
      @PathVariable Long id, @PathVariable String orgSlug) {
    log.info("Fetching staff with id: {}", id);
    return ResponseEntity.ok(staffService.getStaffById(id, orgSlug));
  }

  @GetMapping
  public ResponseEntity<List<StaffDto.StaffResponse>> getAllStaff(@PathVariable String orgSlug) {
    log.info("Fetching all staff members");
    return ResponseEntity.ok(staffService.getAllStaff(orgSlug));
  }

  @PutMapping("/{id}")
  public ResponseEntity<StaffDto.StaffResponse> updateStaff(
      @PathVariable Long id, @RequestBody StaffDto.StaffRequest request) {
    log.info("Updating staff with id: {}", id);
    return ResponseEntity.ok(staffService.updateStaff(id, request));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteStaff(@PathVariable Long id) {
    log.info("Deleting staff with id: {}", id);
    staffService.deleteStaff(id);
    return ResponseEntity.noContent().build();
  }
}
