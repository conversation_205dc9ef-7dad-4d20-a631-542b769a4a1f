package com.wexl.retail.notifications.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum NotificationType {
  CLASSROOM,
  SECTION,
  INDIVIDUAL,
  GRADE,
  <PERSON><PERSON><PERSON><PERSON>ATION,
  CIR<PERSON>LAR,
  LEAVE_REQUEST,
  LEAVE_APPROVED,
  LEAVE_DISAPPROVED,
  APPOINTMENT_REQUEST,
  APPOINTMENT_APPROVED,
  APPOINTMENT_DISAPPROVED,
  EMAIL,
  MESSAGE,
  STAFF_APPOINTMENT
}
