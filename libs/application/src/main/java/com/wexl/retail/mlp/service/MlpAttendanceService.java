package com.wexl.retail.mlp.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;
import static com.wexl.retail.commons.util.DateTimeUtil.getEpochFromStringDate;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.mlp.model.MlpAttendance;
import com.wexl.retail.mlp.repository.MlpAttendanceRepository;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiContentHelper;
import java.time.LocalDate;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MlpAttendanceService {
  private final MlpAttendanceRepository mlpAttendanceRepository;

  private final MlpRepository mlpRepository;

  private final StudentRepository studentRepository;
  private final StrapiContentHelper strapiContentHelper;
  private final OrganizationRepository organizationRepository;

  private final DateTimeUtil dateTimeUtil;

  public void updateMlpAttendance(Mlp mlp, Student student) {

    List<Long> optionalMlpAttendance =
        mlpAttendanceRepository.findAttendanceByStudentIdAndMlpDate(
            student.getId(), mlp.getCreatedAt().toLocalDateTime().toLocalDate().toString());
    if (optionalMlpAttendance.isEmpty()) {
      mlpAttendanceRepository.save(
          MlpAttendance.builder().student(student).mlp(mlp).mlpDate(mlp.getCreatedAt()).build());
    }
  }

  public void updateStudentRecordsByMlpIds(List<Long> mlpIds) {

    for (Long mlpId : mlpIds) {
      var mlp = mlpRepository.findById(mlpId).orElseThrow();
      var mlpAttendanceResult = mlpAttendanceRepository.findAttendanceByMlpIds(mlpId);
      if (!mlpAttendanceResult.isEmpty()) {
        mlpAttendanceResult.forEach(
            result ->
                updateMlpAttendance(
                    mlp, studentRepository.findById(result.getStudentId()).orElseThrow()));
      }
    }
  }

  public List<GenericMetricResponse> getMlpAttendanceSummary(List<String> orgSlugs) {
    LocalDate fromDate = LocalDate.now().minusDays(5);
    LocalDate toDate = LocalDate.now().minusDays(1);
    List<LocalDate> requiredDates = dateTimeUtil.getDates(fromDate, toDate);

    List<MlpAttendanceResult> mlpAttendanceResults =
        mlpAttendanceRepository.getMlpAttendanceByDay(orgSlugs, fromDate, toDate);

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();

    for (var date : requiredDates) {

      var attendanceData =
          mlpAttendanceResults.stream()
              .filter(data -> Objects.equals(date.toString(), data.getMlpDate()))
              .findFirst();

      genericMetricResponses.add(
          GenericMetricResponse.builder()
              .date(getEpochFromStringDate(date.toString()))
              .count(attendanceData.isPresent() ? attendanceData.get().getAttemptedCount() : 0)
              .build());
    }
    GenericMetricResponse totalMlpAttemptedCount = new GenericMetricResponse();
    totalMlpAttemptedCount.setDate(convertIso8601ToEpoch(LocalDate.now().atStartOfDay()));
    totalMlpAttemptedCount.setCount(mlpAttendanceRepository.getMlpAttendance(orgSlugs));
    genericMetricResponses.addFirst(totalMlpAttemptedCount);
    return genericMetricResponses;
  }

  public List<GenericMetricResponse> getMlpAttendanceByDay(
      List<String> orgSlugs, int noOfDays, List<String> gradeList) {
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    LocalDate toDate = LocalDate.now().minusDays(1);
    LocalDate fromDate = toDate.minusDays((long) noOfDays - 1);
    List<LocalDate> requiredDates = dateTimeUtil.getDates(fromDate, toDate);
    Map<String, Object> gradesMap = new LinkedHashMap<>(strapiContentHelper.getGradesMap());
    List<String> keys = new ArrayList<>(gradesMap.keySet());
    List<String> gradesData =
        gradeList.stream().sorted(Comparator.comparingInt(keys::indexOf)).distinct().toList();
    if (gradesData.isEmpty() && orgSlugs.isEmpty()) {
      gradesData =
          List.of(
              "nur", "lkg", "ukg", "i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x",
              "xi", "xii");
    }
    List<MlpAttendancePercentage> mlpAttendancePercentages =
        mlpAttendanceRepository.getMlpAttendancePercentage(
            orgSlugs,
            gradesData,
            requiredDates.getFirst().toString(),
            requiredDates.get(1).toString(),
            requiredDates.get(2).toString(),
            requiredDates.get(3).toString(),
            requiredDates.get(4).toString());
    List<MlpAttendancePercentageQueryResults> mlpStudentDetailByGrade =
        mlpAttendanceRepository.getMlpStudentDetailByGrade(orgSlugs, gradesData, LocalDate.now());

    for (var gradeSlug : gradesData) {

      var mlpAttendance =
          mlpAttendancePercentages.stream()
              .filter(g -> g.getGradeSlug().equals(gradeSlug))
              .findFirst();
      var appUsers =
          mlpStudentDetailByGrade.stream()
              .filter(g -> g.getGradeSlug().equals(gradeSlug))
              .findFirst();

      genericMetricResponses.add(
          GenericMetricResponse.builder()
              .data(mapAttendance(gradesMap, gradeSlug, mlpAttendance, appUsers, requiredDates))
              .build());
    }

    return genericMetricResponses;
  }

  Map<String, Object> mapAttendance(
      Map<String, Object> gradesMap,
      String gradeSlug,
      Optional<MlpAttendancePercentage> mlpAttendances,
      Optional<MlpAttendancePercentageQueryResults> appUsers,
      List<LocalDate> requiredDates) {
    Map<String, Object> map = new HashMap<>();

    double appUserCount = 0.0;

    if (appUsers.isPresent()) {
      var appUser = appUsers.get();
      appUserCount = appUser.getLoginCountDay1();
    }
    double mlpAttemptedCountDay1 = 0.0;
    double mlpAttemptedCountDay2 = 0.0;
    double mlpAttemptedCountDay3 = 0.0;
    double mlpAttemptedCountDay4 = 0.0;
    double mlpAttemptedCountDay5 = 0.0;
    if (mlpAttendances.isPresent()) {
      var mlpAttendance = mlpAttendances.get();
      mlpAttemptedCountDay1 = mlpAttendance.getMlpAttemptedDay1();
      mlpAttemptedCountDay2 = mlpAttendance.getMlpAttemptedDay2();
      mlpAttemptedCountDay3 = mlpAttendance.getMlpAttemptedDay3();
      mlpAttemptedCountDay4 = mlpAttendance.getMlpAttemptedDay4();
      mlpAttemptedCountDay5 = mlpAttendance.getMlpAttemptedDay5();
    }
    double percentageDay1 = calculateMlpAttendancePercentage(mlpAttemptedCountDay1, appUserCount);
    double percentageDay2 = calculateMlpAttendancePercentage(mlpAttemptedCountDay2, appUserCount);
    double percentageDay3 = calculateMlpAttendancePercentage(mlpAttemptedCountDay3, appUserCount);
    double percentageDay4 = calculateMlpAttendancePercentage(mlpAttemptedCountDay4, appUserCount);
    double percentageDay5 = calculateMlpAttendancePercentage(mlpAttemptedCountDay5, appUserCount);

    int percentageDayCount = 0;
    double overAllPercentage = 0.0;

    map.put("slug", gradeSlug);
    map.put("name", gradesMap.get(gradeSlug));
    map.put("total_strength", appUsers.isEmpty() ? 0 : appUsers.get().getTotalStudents());
    map.put("date_day1", DateTimeUtil.getEpochFromStringDate(requiredDates.getFirst().toString()));
    map.put("app_users", appUserCount);
    map.put("mlp_attempted_day1", mlpAttemptedCountDay1);
    map.put("percentage_day1", percentageDay1);
    percentageDayCount = percentageDayCount + percentage(percentageDay1);
    map.put("date_day2", DateTimeUtil.getEpochFromStringDate(requiredDates.get(1).toString()));
    map.put("mlp_attempted_day2", mlpAttemptedCountDay2);
    map.put("percentage_day2", percentageDay2);
    percentageDayCount = percentageDayCount + percentage(percentageDay2);
    map.put("date_day3", DateTimeUtil.getEpochFromStringDate(requiredDates.get(2).toString()));
    map.put("mlp_attempted_day3", mlpAttemptedCountDay3);
    map.put("percentage_day3", percentageDay3);
    percentageDayCount = percentageDayCount + percentage(percentageDay3);
    map.put("date_day4", DateTimeUtil.getEpochFromStringDate(requiredDates.get(3).toString()));
    map.put("mlp_attempted_day4", mlpAttemptedCountDay4);
    map.put("percentage_day4", percentageDay4);
    percentageDayCount = percentageDayCount + percentage(percentageDay4);
    map.put("date_day5", DateTimeUtil.getEpochFromStringDate(requiredDates.get(4).toString()));
    map.put("mlp_attempted_day5", mlpAttemptedCountDay5);
    map.put("percentage_day5", percentageDay5);
    percentageDayCount = percentageDayCount + percentage(percentageDay5);
    if (percentageDayCount > 0) {
      overAllPercentage =
          Double.parseDouble(
              Constants.DECIMAL_FORMAT.format(
                  (percentageDay1
                          + percentageDay2
                          + percentageDay3
                          + percentageDay4
                          + percentageDay5)
                      / percentageDayCount));
    }
    map.put("overAllPercentage", overAllPercentage);
    return map;
  }

  private Integer percentage(Double percentage) {
    if (percentage > 0.0) {
      return 1;
    } else {
      return 0;
    }
  }

  private Double calculateMlpAttendancePercentage(double mlpAttemptedCountDay, double appUserDay) {
    if (mlpAttemptedCountDay == 0 || appUserDay == 0) {
      return 0.0;
    }
    return Double.parseDouble(
        Constants.DECIMAL_FORMAT.format(((mlpAttemptedCountDay / appUserDay) * 100)));
  }

  public List<GenericMetricResponse> getMlpAttendanceAndUserLoginByChildOrg(
      List<String> orgSlugs, int noOfDays) {

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    LocalDate toDate = LocalDate.now().minusDays(1);
    LocalDate fromDate = toDate.minusDays((long) noOfDays - 1);
    List<LocalDate> requiredDates = dateTimeUtil.getDates(fromDate, toDate);

    var childOrgsData = orgSlugs.stream().distinct().toList();

    List<MlpAttendancePercentage> mlpAttendancePercentages =
        mlpAttendanceRepository.getMlpAttendancePercentageOrgWise(
            childOrgsData,
            requiredDates.getFirst().toString(),
            requiredDates.get(1).toString(),
            requiredDates.get(2).toString(),
            requiredDates.get(3).toString(),
            requiredDates.get(4).toString());
    List<MlpAttendancePercentageQueryResults> mlpStudentDetailByGrade =
        mlpAttendanceRepository.getMlpStudentDetailChildOrgWise(childOrgsData, LocalDate.now());
    List<Organization> organizations = organizationRepository.findBySlug(orgSlugs);
    Map<String, Object> orgsMap = new HashMap<>();

    for (var org : organizations) {
      orgsMap.put(org.getSlug(), org.getName());
    }

    for (var childOrgSlug : childOrgsData) {

      var mlpAttendance =
          mlpAttendancePercentages.stream()
              .filter(g -> g.getOrganizationSlug().equals(childOrgSlug))
              .findFirst();
      var appUsers =
          mlpStudentDetailByGrade.stream()
              .filter(g -> g.getOrganizationSlug().equals(childOrgSlug))
              .findFirst();

      genericMetricResponses.add(
          GenericMetricResponse.builder()
              .data(mapAttendance(orgsMap, childOrgSlug, mlpAttendance, appUsers, requiredDates))
              .build());
    }

    return genericMetricResponses;
  }
}
