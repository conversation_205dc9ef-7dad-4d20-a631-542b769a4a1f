package com.wexl.retail.elp.controller;

import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.elp.service.ElpService;
import com.wexl.retail.test.school.dto.QuestionDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class SmartBoardElpController {

  private final ElpService elpService;

  @GetMapping("/tasks/{taskId}/elp-smart-board/questions")
  public ElpDto.SmartBoardElpQuestionResponse getElpQuestions(@PathVariable("taskId") Long taskId) {
    return elpService.getElpQuestions(taskId);
  }

  @PostMapping("/tasks/{taskId}/elp-smart-board:attempt")
  public ElpDto.SmartBoardAttemptResponse attemptElpQuestionToStudent(
      @PathVariable("orgSlug") String orgSlug,
      @RequestBody ElpDto.AttemptQuestionRequest attemptQuestionRequest,
      @PathVariable("taskId") Long taskId) {
    return elpService.attemptElpQuestionByStudent(orgSlug, attemptQuestionRequest, taskId);
  }

  @GetMapping("/exams/{examId}/elp-smart-board:results")
  public QuestionDto.StudentResultsResponse getSmartBoardResult(
      @PathVariable("examId") Long examId) {
    return elpService.getSmartBoardResult(examId);
  }
}
