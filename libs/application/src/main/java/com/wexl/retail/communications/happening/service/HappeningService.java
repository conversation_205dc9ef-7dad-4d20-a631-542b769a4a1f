package com.wexl.retail.communications.happening.service;

import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.communications.happening.dto.HappeningDto;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.UserRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HappeningService {
  private final NotificationsService notificationService;
  private final NotificationRepository notificationRepository;
  private final UserRepository userRepository;

  public void createHappeningNotification(
      String orgSlug, HappeningDto.HappeningRequest happeningRequest, String teacherAuthId) {
    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(happeningRequest.title())
            .message(happeningRequest.message())
            .attachment(happeningRequest.attachment())
            .link(happeningRequest.link())
            .notificationType(NotificationType.ORGANIZATION)
            .orgSlugs(List.of(orgSlug))
            .feature(CommunicationFeature.HAPPENING)
            .build();
    notificationService.createNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, false);
  }

  public HappeningDto.HappeningResponse getHappeningNotifications(String orgSlug) {
    var happeningNotifications =
        notificationRepository.findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
            orgSlug, CommunicationFeature.HAPPENING);
    var notificationResponse =
        notificationService.buildNotificationResponse(happeningNotifications, 100);
    return HappeningDto.HappeningResponse.builder().notifications(notificationResponse).build();
  }

  public void updateHappeningNotification(
      String orgSlug, Long notificationId, HappeningDto.HappeningRequest request) {
    var notification = notificationService.getNotificationByIdAndOrgSlug(notificationId, orgSlug);
    notification.setTitle(request.title());
    notification.setMessage(request.message());
    notification.setAttachments(request.attachment());
    notification.setLink(request.link());
    notificationRepository.save(notification);
  }
}
