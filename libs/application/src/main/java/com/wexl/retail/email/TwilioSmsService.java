package com.wexl.retail.email;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TwilioSmsService {

  private static final String MAGIC_MOBILE_NUMBER_PREFIX = "000";
  private static final String DEFAULT_COUNTRY_PREFIX = "+91";

  private boolean isMagicMobileNumber(String mobileNumber) {
    return mobileNumber.startsWith(MAGIC_MOBILE_NUMBER_PREFIX);
  }

  public String massageMobileNumber(String mobileNumber) {
    if (Objects.isNull(mobileNumber)) {
      return "";
    }
    if (mobileNumber.trim().length() == 10) {
      return DEFAULT_COUNTRY_PREFIX + mobileNumber.trim();
    }
    return mobileNumber.trim();
  }

  public void sendSmsToMobileNo(String mobileNo, String notification) {
    if (Objects.isNull(mobileNo) || isMagicMobileNumber(mobileNo)) {
      return;
    }
    var mobileNumber = massageMobileNumber(mobileNo);
    sendSmsUsingTwilio(mobileNumber, notification);
  }

  private void sendSmsUsingTwilio(String mobileNumber, String notification) {
    throw new ApiException(
        InternalErrorCodes.SERVER_ERROR,
        "error.serverError.1",
        new String[] {"Unable to send SMS via Twilio"});
  }
}
