package com.wexl.retail.student.auth;

import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto;
import com.wexl.retail.globalprofile.service.RoleTemplateService;
import com.wexl.retail.guardian.dto.GuardianDto;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.OrgStudent;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserRole;
import com.wexl.retail.model.UserVerificationStatus;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.Status;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class StudentAuthTransformer {

  @Autowired private StrapiService strapiService;

  @Autowired private ContentService contentService;
  @Autowired private CurriculumService curriculumService;
  @Autowired private SectionRepository sectionRepository;
  @Autowired private RoleTemplateService roleTemplateService;
  @Autowired private UserRoleHelper userRoleHelper;
  @Autowired private UserService userService;
  @Autowired private OrganizationRepository organizationRepository;

  public StudentSignupRequest mapStudentSignupRequest(StudentRequest studentRequest) {

    final Grade grade = contentService.getGradeBySlug(studentRequest.getGradeSlug());
    int boardId;
    if (StringUtils.isNotBlank(studentRequest.getBoardSlug())) {
      Entity board = strapiService.getEduBoardBySlug(studentRequest.getBoardSlug());
      boardId = board.getId();
    } else {
      EduBoard eduBoard =
          curriculumService.getBoardByGrade(
              studentRequest.getOrgSlug(), studentRequest.getGradeSlug());
      boardId = eduBoard.getId();
    }
    List<String> sections = new ArrayList<>();
    var sectionList =
        sectionRepository.getSectionsOfGrade(studentRequest.getOrgSlug(), grade.getId());
    sectionList.stream().map(Section::getName).forEach(sections::add);
    if (!sections.contains(studentRequest.getSection())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.section.grade",
          new String[] {studentRequest.getSection(), grade.getName()});
    }

    final var academicYear =
        contentService.getAcademicYearBySlug(
            studentRequest.getOrgSlug(), studentRequest.getAcademicYearSlug());
    var roleTemplate =
        studentRequest.getRoleTemplate() != null
            ? roleTemplateService.getRoleTemplateById(studentRequest.getRoleTemplate().getId())
            : userRoleHelper.findRoleTemplateByOrgSlug(
                studentRequest.getOrgSlug(), UserRole.ROLE_ISTUDENT);
    return StudentSignupRequest.builder()
        .boardId(boardId)
        .classId(grade.getId())
        .mobileNumber(studentRequest.getMobileNumber())
        .countryCode(studentRequest.getCountryCode())
        .firstName(studentRequest.getFirstName())
        .lastName(studentRequest.getLastName())
        .parentEmail(studentRequest.getParentEmail())
        .parentFirstName(studentRequest.getParentFirstName())
        .parentLastName(studentRequest.getParentLastName())
        .parentMobileNumber(studentRequest.getParentMobileNumber())
        .password(studentRequest.getPassword())
        .schoolName(studentRequest.getSchoolName())
        .userName(studentRequest.getUserName())
        .termsAndConditions(true)
        .gender(studentRequest.getGender())
        .email(studentRequest.getEmail())
        .rollNumber(studentRequest.getRollNumber())
        .externalRef(studentRequest.getExternalRef())
        .academicYearSlug(academicYear.getSlug())
        .section(studentRequest.getSection())
        .orgSlug(studentRequest.getOrgSlug())
        .crStudentUserName(studentRequest.getCrStudentUserName())
        .attributes(studentRequest.getAttributes())
        .roleTemplate(roleTemplate)
        .classRollNumber(studentRequest.getClassRollNumber())
        .isFeePaid(studentRequest.isFeePaid())
        .build();
  }

  public Student mapStudentSignupRequest(
      StudentSignupRequest studentInfo,
      String userName,
      UserVerificationStatus userVerificationStatus,
      String orgSlug) {

    var student = new Student();
    student.setSchoolName(studentInfo.getSchoolName());
    student.setBoardId(studentInfo.getBoardId());
    student.setClassId(studentInfo.getClassId());
    student.setRollNumber(studentInfo.getRollNumber());
    student.setClassRollNumber(student.getClassRollNumber());
    student.setAcademicYearSlug(studentInfo.getAcademicYearSlug());
    student.setAttributes(studentInfo.getAttributes());
    student.setFeeDefaulter(
        Objects.isNull(studentInfo.getIsFeePaid()) || studentInfo.getIsFeePaid());
    student.setRoleTemplate(
        Objects.nonNull(studentInfo.roleTemplate)
            ? studentInfo.roleTemplate
            : userRoleHelper.findRoleTemplateByOrgSlug(
                studentInfo.getOrgSlug(), UserRole.ROLE_ISTUDENT));

    var user = new User();
    user.setFirstName(studentInfo.getFirstName());
    user.setLastName(studentInfo.getLastName());
    user.setUserName(studentInfo.getUserName());
    user.setAuthUserId(userName);
    user.setUserName(userName);
    user.setVerificationStatus(userVerificationStatus);
    user.setGender(studentInfo.getGender());
    user.setEmail(studentInfo.getEmail());
    user.setOrganization(orgSlug);
    user.setCountryCode(userService.validateCountryCode(studentInfo.getCountryCode()));
    student.setUserInfo(user);

    return student;
  }

  public BulkStudentSignup mapStudentSignupToBulkStudentSignup(
      StudentSignupRequest studentSignupRequest) {
    return BulkStudentSignup.builder()
        .boardId(studentSignupRequest.getBoardId())
        .classId(studentSignupRequest.getClassId())
        .firstName(studentSignupRequest.getFirstName())
        .lastName(studentSignupRequest.getLastName())
        .parentEmail(studentSignupRequest.getParentEmail())
        .parentFirstName(studentSignupRequest.getParentFirstName())
        .parentLastName(studentSignupRequest.getParentLastName())
        .parentMobileNumber(studentSignupRequest.getParentMobileNumber())
        .password(studentSignupRequest.getPassword())
        .schoolName(studentSignupRequest.getSchoolName())
        .userName(studentSignupRequest.getUserName())
        .build();
  }

  public StudentResponse studentResponseFrom(
      User student,
      String grade,
      String board,
      String schoolName,
      String rollNo,
      String classRollNumber,
      String academicYearSlug,
      String section,
      String guid) {
    return StudentResponse.builder()
        .boardSlug(board)
        .gradeSlug(grade)
        .firstName(student.getFirstName())
        .lastName(student.getLastName())
        .parentEmail("")
        .parentFirstName("")
        .parentLastName("")
        .parentMobileNumber("")
        .schoolName(schoolName)
        .userName(student.getUserName())
        .gender(student.getGender())
        .email(student.getEmail())
        .mobileNumber(student.getMobileNumber())
        .countryCode(
            Objects.nonNull(student.getCountryCode())
                ? student.getCountryCode()
                : Constants.DEFAULT_COUNTRY_CODE)
        .rollNumber(rollNo)
        .classRollNumber(classRollNumber)
        .academicYearSlug(academicYearSlug)
        .section(section)
        .guid(guid)
        .guardianDetails(
            student.getStudentInfo() != null
                ? buildGuardianDetails(student.getStudentInfo().getGuardians())
                : null)
        .roleTemplates(
            student.getStudentInfo() == null
                ? null
                : getStudentRoleTemplate(student.getStudentInfo()))
        .build();
  }

  private GlobalProfileDto.RoleTemplateResponse getStudentRoleTemplate(Student studentInfo) {
    return studentInfo.getRoleTemplate() == null
        ? null
        : roleTemplateService.getRoleTemplates(studentInfo.getRoleTemplate());
  }

  private List<GuardianDto.Response> buildGuardianDetails(List<Guardian> guardians) {
    List<GuardianDto.Response> guardianList = new ArrayList<>();
    guardians.forEach(
        guardian ->
            guardianList.add(
                GuardianDto.Response.builder()
                    .mobileNumber(guardian.getMobileNumber())
                    .email(guardian.getEmail())
                    .firstName(guardian.getFirstName())
                    .lastName(guardian.getLastName())
                    .studentId(guardian.getStudent().getId())
                    .id(guardian.getId())
                    .build()));
    return guardianList;
  }

  public List<StudentResponse> studentsResponseFrom(String orgSlug, List<OrgStudent> students) {
    List<Grade> allGrades = new ArrayList<>(strapiService.getAllGrades());
    List<Entity> allBoards = new ArrayList<>(strapiService.getAllBoards());
    return students.stream()
        .map(student -> getStudentResponse(orgSlug, allGrades, allBoards, student))
        .toList();
  }

  public StudentResponse getStudentResponse(
      String orgSlug, List<Grade> allGrades, List<Entity> allBoards, OrgStudent student) {
    var org = organizationRepository.findBySlug(orgSlug);
    final var studentResponse =
        StudentResponse.builder()
            .id(student.getStudentId())
            .userName(student.getUsername())
            .firstName(student.getFirstName())
            .lastName(student.getLastName())
            .section(student.getSectionName())
            .admissionNumber(
                (student.getRollNumber() == null || student.getRollNumber().isEmpty())
                    ? "NA"
                    : student.getRollNumber())
            .gradeId(student.getClassId())
            .parentFirstName(student.getParentFirstName())
            .parentLastName(student.getParentLastName())
            .parentEmail(student.getParentEmail())
            .parentMobileNumber(student.getParentMobileNumber())
            .isDisconnected(isDisconnected(student.getDeletedAt()))
            .guid(student.getGuid())
            .lastLoginTime(getLastLogin(student.getLastLoginTime()))
            .organization(org.getName())
            .classRollNumber(student.getClassRollNumber())
            .isFeePaid(Objects.isNull(student.getFeeDefaulter()) || !student.getFeeDefaulter())
            .build();

    final Optional<Grade> studentGrade =
        allGrades.stream().filter(grade -> grade.getId() == student.getClassId()).findFirst();
    if (studentGrade.isPresent()) {
      studentResponse.setGradeSlug(studentGrade.get().getSlug());
      studentResponse.setGradeName(studentGrade.get().getName());
    } else {
      log.error("Student grade not found in strapi");
    }

    final Optional<Entity> studentBoard =
        allBoards.stream().filter(board -> board.getId() == student.getBoardId()).findFirst();
    if (studentBoard.isPresent()) {
      studentResponse.setBoardSlug(studentBoard.get().getSlug());
      studentResponse.setBoardName(studentBoard.get().getAssetName());
    } else {
      log.error("Student board not found in strapi");
    }
    return studentResponse;
  }

  public StudentResponse getFilterStudentResponse(Student student) {
    return StudentResponse.builder()
        .id(student.getId())
        .userName(student.getUserInfo().getUserName())
        .firstName(student.getUserInfo().getFirstName())
        .lastName(student.getUserInfo().getLastName())
        .section(student.getSection().getName())
        .gradeId(student.getClassId())
        .gradeName(student.getSection().getGradeName())
        .gradeSlug(student.getSection().getGradeSlug())
        .boardSlug(student.getSection().getBoardSlug())
        .boardName(student.getSection().getBoardName())
        .parentFirstName(
            student.getGuardians().isEmpty() ? null : student.getGuardians().get(0).getFirstName())
        .parentLastName(
            student.getGuardians().isEmpty() ? null : student.getGuardians().get(0).getLastName())
        .parentEmail(student.getUserInfo().getEmail())
        .parentMobileNumber(student.getUserInfo().getMobileNumber())
        .isDisconnected(isDisconnected(student.getUserInfo().getDeletedAt()))
        .guid(student.getUserInfo().getGuid())
        .lastLoginTime(getLastLogin(student.getUserInfo().getLastLogin()))
        .organization(student.getUserInfo().getOrganization())
        .classRollNumber(student.getClassRollNumber())
        .isFeePaid(Objects.isNull(student.getFeeDefaulter()) || student.getFeeDefaulter())
        .build();
  }

  public Long getLastLogin(Date date) {
    if (date != null) {
      return date.getTime();
    }
    return null;
  }

  public Status isDisconnected(Date deletedAt) {
    return deletedAt != null ? Status.INACTIVE : Status.ACTIVE;
  }
}
