package com.wexl.retail.commons.util;

public class JwtConstants {
  public static final String FIRST_NAME = "firstName";
  public static final String LAST_NAME = "lastName";
  public static final String USER_NAME = "userName";
  public static final String MOBILE_NUMBER = "mobileNumber";
  public static final String ROLES = "roles";
  public static final String SCHOOL_NAME = "schoolName";
  public static final String GRADE = "grade";
  public static final String VERIFICATION_STATUS = "verificationStatus";
  public static final String ADDRESS = "address";
  public static final String IS_MOBILE = "mob";
  public static final String ORGANIZATION = "organization";
  public static final String CLASS_ID = "classId";
  public static final String STUDENT_ID = "studentId";
  public static final String IS_PREMIUM = "hasPremiumSubscription";
  public static final String BOARD_ID = "boardId";
  public static final String BOARD_NAME = "boardName";
  public static final String TEACHER_DETAILS_ID = "teacherDetailsId";
  public static final String INDIA = "IN";
  public static final String ID = "id";
  public static final String SECTIONS = "sections";
  public static final String SUBJECTS = "subjects";
  public static final String SCOPE = "scope";
  public static final String EMAIL = "email";
  public static final String GUID = "guid";
  public static final String SUB = "sub";
  public static final String EXP = "exp";

  public static final String LOGIN_METHOD = "loginMethod";

  private JwtConstants() {
    throw new IllegalStateException("Utility class");
  }
}
