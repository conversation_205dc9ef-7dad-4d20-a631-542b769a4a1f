<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin-top="18mm"  margin-left="5mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <!--    PAGE 1-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-10%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="278%" content-height="278%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="350%" height="350%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feColorMatrix type="matrix" values="0.5 0 0 0 0
                                                                          0 2 0 0 0
                                                                          0 0 0.1 0 0
                                                                          0 0 0 0.3 0"/>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/BET+BG.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:table border="none" width="100%">
                    <fo:table-column column-width="100%" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="0.5cm" text-align="center">
                                    <fo:external-graphic
                                            src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/GILLCO_INTERNATIONAL_SCHOOL_LOGO_New.png")'
                                            content-width="150px"
                                            content-height="auto"
                                            scaling="uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block font-size="22pt" font-weight="bold" text-align="center" space-before="3mm">
                    GILLCO INTERNATIONAL SCHOOL,
                    <fo:block>MOHALI</fo:block>
                </fo:block>

                <fo:block font-size="14pt" font-family="Times New Roman"
                          text-align="center" color="#811c22" font-weight="bold" padding-top="1cm" space-after="3mm">
                    HOLISTIC PROGRESS REPORT
                </fo:block>



                <fo:table margin-left="9mm">
                    <fo:table-column column-width="70mm"/>
                    <fo:table-column column-width="100%"/>
                    <fo:table-column column-width="0mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <!-- Empty left spacer -->
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>

                            <!-- Centered SVG Image -->
                            <fo:table-cell>
                                <fo:block text-align="center" padding-bottom="5mm">
                                    <fo:block-container width="28mm" height="38mm"
                                                        border="5pt solid #d5e6e8"
                                                        background-color="white"
                                                        display-align="center"
                                                        text-align="center"
                                                        margin-left="3mm"
                                                        margin-right="auto"
                                                        padding="1mm">
                                        <fo:block margin-right="5mm" font-size="10pt" font-family="Arial, sans-serif" text-align="center">
                                            <fo:instream-foreign-object content-width="100%" content-height="100%">
                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                     xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <defs>
                                                        <filter id="brightnessFilter">
                                                            <feComponentTransfer>
                                                                <feFuncR type="linear" slope="1"/>
                                                                <feFuncG type="linear" slope="1"/>
                                                                <feFuncB type="linear" slope="1"/>
                                                            </feComponentTransfer>
                                                        </filter>
                                                    </defs>
                                                    <image filter="url(#brightnessFilter)"
                                                           x="0" y="0" width="100%" height="100%"
                                                           th:xlink:href="@{${model.header.imageUrl}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:block>
                            </fo:table-cell>

                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


                <fo:block-container border="1pt solid black"  background-color="#fdf2e9" padding="5mm"  margin-top="12pt"
                                    margin-bottom="12pt" display-align="center" margin-left="10mm" width="170mm">
                    <fo:block font-size="13pt" line-height="16pt">

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="50mm"/>
                            <fo:table-column column-width="115mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Name of the student :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.name}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>


                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="15mm"/>
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="18mm"/>
                            <fo:table-column column-width="38mm"/>
                            <fo:table-column column-width="20mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-body >
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Class</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.className}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Section</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.sectionName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Roll No.</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.rollNo}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>


                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="33mm"/>
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="16mm"/>
                            <fo:table-column column-width="28mm"/>
                            <fo:table-column column-width="12mm"/>
                            <fo:table-column column-width="41mm"/>
                            <fo:table-body >
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Admission No.</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.admissionNumber}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">House</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.house}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">DOB</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.dateOfBirth}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="20mm"/>
                            <fo:table-column column-width="145mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Address</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.address}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="33mm"/>
                            <fo:table-column column-width="132mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Father's Name</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.fatherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="130mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Mother's Name</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.motherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!-- PAGE 2 -->

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-17mm" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="262%" content-height="262%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="327%" height="327%" viewBox="0 0 100 100">

                                <image  x="0" y="0" width="100%" height="100%" xlink:href="https://images-ext-1.discordapp.net/external/xAFK3a9LiLSqjG5R6QZuvwaYecT9UHt_QpUp9lnwqFs/https/s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/Holistic%2520Progress%2520Report%25202nd%2520page-1.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!-- PAGE 3 -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-10%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="278%" content-height="278%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="350%" height="350%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feColorMatrix type="matrix" values="0.5 0 0 0 0
                                                                          0 2 0 0 0
                                                                          0 0 0.1 0 0
                                                                          0 0 0 0.3 0"/>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/BET+BG.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!-- PAGE 4 --> <!-- TERM 1 -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" >

                <fo:block font-weight="bold" margin-left="-7mm" font-size="12pt" text-align="center" color="red">TERM - I</fo:block>
                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="2mm" padding-left="12mm">LANGUAGE &amp; LITERACY DEVELOPMENT</fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="36.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>

                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm"  border="1pt solid black" text-align="left">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Listening</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Speaking</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Reading</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Writing</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Vocabulary</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term1.languageAndLiteracy}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.listening}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.speaking}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.reading}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.writing}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.vocabulary}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold"  font-size="11pt" space-before="2mm" margin-left="4.5mm">COGNITIVE DEVELOPMENT</fo:block>
                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" >
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left">MATHEMATICAL UNDERSTANDING OF</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Knowledge</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Computing Skills</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Application</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term1.cognitiveDevelopment}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.mathematical}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.knowledge}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.computingSkills}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.application}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold"  font-size="11pt" space-before="2mm" margin-left="5mm" >ENVIRONMENTAL AWARENESS DEVELOPMENT</fo:block>
                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" >
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left">THEMES</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Knowledge</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Attitude</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Application</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term1.environMentalAwareness}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.themes}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.knowledge}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.attitude}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.application}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold" margin-left="5mm" font-size="11pt" space-before="2mm">
                    PHYSICAL DEVELOPMENT
                </fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="36.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>

                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Creativity</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" >
                                    <fo:block text-align="center">Aesthetic Value</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Pincer Grip</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Eye Hand Coordination</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Eye Foot Coordination</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term1.physicalDevelopment.table1}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.creativity}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.aestheticValue}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.pincerGrip}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeHandCoordination}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeFootCoordination}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block border-width="2mm" font-size="10pt" space-before="4mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="20.6mm"/>
                        <fo:table-column column-width="35.5mm"/>
                        <fo:table-column column-width="32.5mm"/>
                        <fo:table-column column-width="33.5mm"/>
                        <fo:table-column column-width="33.5mm"/>
                        <fo:table-column column-width="33.5mm"/>

                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block padding-left="-5mm" text-align="left">Balance/Coordination</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" >
                                    <fo:block text-align="center">Locomotor Skills</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Manipulative Skills</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Physical Strength</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Height/weight</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term1.physicalDevelopment.table2}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.creativity}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.aestheticValue}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.pincerGrip}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeHandCoordination}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeFootCoordination}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- FIFTH PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" >

                <fo:block font-weight="bold" margin-left="-7mm" font-size="12pt" text-align="center" color="red">TERM - I</fo:block>
                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="2mm" padding-left="12mm">SOCIO-EMOTIONAL DEVELOPMENT</fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term1.socioEmotionalDevelopment}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.skillValue}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="2mm" padding-left="12mm">LEARNING SKILLS</fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term1.learningSkills}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.skillValue}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="4mm" padding-left="12mm">Class Teacher Remarks</fo:block>
                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="188.5mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${model.body.term1.remarks}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block border-width="2mm" font-size="10pt" space-before="22mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" display-align="center" border-bottom="0.5pt solid black">
                                    <fo:block margin-left="10mm" text-align="left">Class Teacher</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" display-align="center" border-bottom="0.5pt solid black">
                                    <fo:block margin-right="10mm" text-align="right">Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="10mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="2" border="1pt solid black">
                                    <fo:block text-align="center" font-weight="bold" font-size="11pt" padding="2mm">GRADING SYSTEM</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="2" padding="4mm" border="1pt solid black">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="2" border="1pt solid black">
                                    <fo:block text-align="center" font-size="11pt" font-weight="bold" padding="1.5mm" th:text="${model.body.term1.gradingSystem.title}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1.5mm" border="1pt solid black">
                                    <fo:block text-align="center" font-size="11pt" font-weight="bold">GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1.5mm" border="1pt solid black">
                                    <fo:block text-align="center" font-size="11pt" font-weight="bold">DISCRIPTION</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="data : ${model.body.term1.gradingSystem.gradeSystemDetails}">
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.grade}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.discription}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!-- PAGE 6 --> <!-- TERM 2 -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" >

                <fo:block font-weight="bold" margin-left="-7mm" font-size="12pt" text-align="center" color="red">TERM - II</fo:block>
                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="2mm" padding-left="12mm">LANGUAGE &amp; LITERACY DEVELOPMENT</fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="36.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>

                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm"  border="1pt solid black" text-align="left">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Listening</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Speaking</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Reading</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Writing</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Vocabulary</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term2.languageAndLiteracy}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.listening}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.speaking}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.reading}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.writing}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.vocabulary}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold"  font-size="11pt" space-before="2mm" margin-left="4.5mm">COGNITIVE DEVELOPMENT</fo:block>
                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" >
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left">MATHEMATICAL UNDERSTANDING OF</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Knowledge</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Computing Skills</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Application</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term2.cognitiveDevelopment}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.mathematical}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.knowledge}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.computingSkills}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.application}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold"  font-size="11pt" space-before="2mm" margin-left="5mm" >ENVIRONMENTAL AWARENESS DEVELOPMENT</fo:block>
                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" >
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-column column-width="47.25mm"/>
                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left">THEMES</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Knowledge</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Attitude</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center">Application</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term2.environMentalAwareness}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.themes}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.knowledge}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.attitude}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.application}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold" margin-left="5mm" font-size="11pt" space-before="2mm">
                    PHYSICAL DEVELOPMENT
                </fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="36.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>
                        <fo:table-column column-width="30.5mm"/>

                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Creativity</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" >
                                    <fo:block text-align="center">Aesthetic Value</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Pincer Grip</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Eye Hand Coordination</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Eye Foot Coordination</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term2.physicalDevelopment.table1}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.creativity}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.aestheticValue}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.pincerGrip}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeHandCoordination}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeFootCoordination}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block border-width="2mm" font-size="10pt" space-before="4mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="20.6mm"/>
                        <fo:table-column column-width="35.5mm"/>
                        <fo:table-column column-width="32.5mm"/>
                        <fo:table-column column-width="33.5mm"/>
                        <fo:table-column column-width="33.5mm"/>
                        <fo:table-column column-width="33.5mm"/>

                        <fo:table-header font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block padding-left="-5mm" text-align="left">Balance/Coordination</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" >
                                    <fo:block text-align="center">Locomotor Skills</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Manipulative Skills</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Physical Strength</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block text-align="center">Height/weight</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term2.physicalDevelopment.table2}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.creativity}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.aestheticValue}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.pincerGrip}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeHandCoordination}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.eyeFootCoordination}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- PAGE 7 -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" >

                <fo:block font-weight="bold" margin-left="-7mm" font-size="12pt" text-align="center" color="red">TERM - II</fo:block>
                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="2mm" padding-left="12mm">SOCIO-EMOTIONAL DEVELOPMENT</fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term2.socioEmotionalDevelopment}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.skillValue}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="2mm" padding-left="12mm">LEARNING SKILLS</fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-body>
                            <fo:table-row th:each="data : ${model.body.term2.learningSkills}">
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${data.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.skillValue}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-weight="bold" margin-left="-7mm" font-size="11pt" space-before="4mm" padding-left="12mm">Class Teacher Remarks</fo:block>
                <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="188.5mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="left" th:text="${model.body.term2.remarks}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block border-width="2mm" font-size="10pt" space-before="22mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" display-align="center" border-bottom="0.5pt solid black">
                                    <fo:block margin-left="10mm" text-align="left">Class Teacher</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" display-align="center" border-bottom="0.5pt solid black">
                                    <fo:block margin-right="10mm" text-align="right">Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="2mm" font-size="10pt" space-before="10mm" padding-left="5mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="5mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-column column-width="94.25mm"/>
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="2" border="1pt solid black">
                                    <fo:block text-align="center" font-weight="bold" font-size="11pt" padding="2mm">GRADING SYSTEM</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="2" padding="4mm" border="1pt solid black">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="2" border="1pt solid black">
                                    <fo:block text-align="center" font-size="11pt" font-weight="bold" padding="1.5mm" th:text="${model.body.term2.gradingSystem.title}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1.5mm" border="1pt solid black">
                                    <fo:block text-align="center" font-size="11pt" font-weight="bold">GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1.5mm" border="1pt solid black">
                                    <fo:block text-align="center" font-size="11pt" font-weight="bold">DISCRIPTION</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="data : ${model.body.term2.gradingSystem.gradeSystemDetails}">
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.grade}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black" display-align="center">
                                    <fo:block text-align="center" th:text="${data.discription}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!-- LAST PAGE-->

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-17mm" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="262%" content-height="262%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="327%" height="327%" viewBox="0 0 100 100">

                                <image  x="0" y="0" width="100%" height="100%" xlink:href="https://images-ext-1.discordapp.net/external/stPH7f10-GZmY8gaTQ0jSXI_sLu8cb_XWrO48vY4sQk/https/s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/Holistic%2520Progress%2520Report%25208th%2520page-1.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

</fo:root>
